<?php

namespace App\Models;

use App\Enums\Loan\LoanPaymentStatus;
use App\Traits\DateTimeConversion;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LoanPayment extends BaseModel
{
    use DateTimeConversion, HasFactory, UniqueCodeTrait;

    protected $fillable = [
        'uuid',
        'code',
        'loan_id',
        'txn_date',
        'txn_type',
        'payment_ref_code',
        'payment_method',
        'payment_date',
        'amount',
        'rebate_amount',
        'remark',
        'status',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'loan_id' => 'integer',
        'txn_date' => 'datetime',
        'payment_date' => 'datetime',
        'amount' => 'decimal:2',
        'rebate_amount' => 'decimal:2',
        'status' => LoanPaymentStatus::class,
        'deleted_at' => 'datetime',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
    ];

    /**
     * Get the loan associated with this payment.
     */
    public function loan()
    {
        return $this->belongsTo(Loan::class);
    }

    /**
     * Get the payment details for this payment.
     */
    public function paymentDetails(): HasMany
    {
        return $this->hasMany(LoanPaymentDetail::class);
    }

    public function scopeWithLoanId($query, $loanId)
    {
        return $query->where('loan_id', $loanId);
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new loan payment.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix('LP');
    }

    public function setPaymentDateAttribute($value)
    {
        $this->attributes['payment_date'] = $this->parseDateWithTimezone($value);
    }

    public function getPaymentDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }
}
